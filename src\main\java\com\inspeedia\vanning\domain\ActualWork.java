package com.inspeedia.vanning.domain;

import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Objects;

/**
 * ActualWork entity representing actual work performed in the VMA system
 *
 * This entity stores information about actual work including user, time
 * details, and progress information.
 */
@Table("actual_work")
public class ActualWork extends BaseEntity {

    @NotBlank(message = "Operator name is required")
    @Size(max = 100, message = "Operator name must not exceed 100 characters")
    @Column("operator_name")
    private String operatorName;

    @NotNull(message = "Date is required")
    @Column("work_date")
    private LocalDate workDate;

    @NotNull(message = "Start time is required")
    @Column("start_time")
    private LocalTime startTime;

    @NotNull(message = "End time is required")
    @Column("end_time")
    private LocalTime endTime;

    @NotNull(message = "Duration is required")
    @Column("duration")
    private LocalTime duration;

    @NotNull(message = "Progress is required")
    @Min(value = 0, message = "Progress must be between 0 and 10")
    @Max(value = 10, message = "Progress must be between 0 and 10")
    @Column("progress")
    private Integer progress;

    @NotNull(message = "Progress rate is required")
    @DecimalMin(value = "0.0", message = "Progress rate must be between 0.0 and 100.0")
    @DecimalMax(value = "100.0", message = "Progress rate must be between 0.0 and 100.0")
    @Column("progress_rate")
    private Float progressRate;

    @NotBlank(message = "Van GP is required")
    @Size(min = 2, max = 2, message = "Van GP must be exactly 2 characters")
    @Pattern(regexp = "^[A-Z0-9]{2}$", message = "Van GP must contain only uppercase letters and numbers")
    @Column("van_gp")
    private String vanGp;

    @Column("completed")
    private Boolean completed = false;

    // Default constructor
    public ActualWork() {
        super();
    }

    // Constructor with all fields
    public ActualWork(String operatorName, LocalDate workDate, LocalTime startTime, LocalTime endTime, LocalTime duration,
            Integer progress, Float progressRate, String vanGp, Boolean completed) {
        super();
        this.workDate = workDate;
        this.operatorName = operatorName;
        this.startTime = startTime;
        this.endTime = endTime;
        this.duration = duration;
        this.progress = progress;
        this.progressRate = progressRate;
        this.vanGp = vanGp;
        this.completed = completed;
    }

    // Constructor with all fields including base entity fields
    public ActualWork(Long id, LocalDateTime createdAt, LocalDateTime updatedAt, Long version,
            String createdBy, String updatedBy, boolean deleted,
            String operatorName, LocalDate workDate, LocalTime startTime, LocalTime endTime, LocalTime duration,
            Integer progress, Float progressRate, String vanGp, Boolean completed) {
        super(id, createdAt, updatedAt, version, createdBy, updatedBy, deleted);
        this.operatorName = operatorName;
        this.workDate = workDate;
        this.startTime = startTime;
        this.endTime = endTime;
        this.duration = duration;
        this.progress = progress;
        this.progressRate = progressRate;
        this.vanGp = vanGp;
        this.completed = completed;
    }

    // Getters
    public String getOperatorName() {
        return operatorName;
    }

    public LocalDate getWorkDate() {
        return workDate;
    }

    public LocalTime getStartTime() {
        return startTime;
    }

    public LocalTime getEndTime() {
        return endTime;
    }

    public LocalTime getDuration() {
        return duration;
    }

    public Integer getProgress() {
        return progress;
    }

    public Float getProgressRate() {
        return progressRate;
    }

    public String getVanGp() {
        return vanGp;
    }

    public Boolean getCompleted() {
        return completed;
    }

    // Setters
    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public void setWorkDate(LocalDate workDate) {
        this.workDate = workDate;
    }

    public void setStartTime(LocalTime startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(LocalTime endTime) {
        this.endTime = endTime;
    }

    public void setDuration(LocalTime duration) {
        this.duration = duration;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public void setProgressRate(Float progressRate) {
        this.progressRate = progressRate;
    }

    public void setVanGp(String vanGp) {
        this.vanGp = vanGp;
    }

    public void setCompleted(Boolean completed) {
        this.completed = completed;
    }

    // equals method (calls super)
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        ActualWork that = (ActualWork) o;
        return Objects.equals(operatorName, that.operatorName)
                && Objects.equals(workDate, that.workDate)
                && Objects.equals(startTime, that.startTime)
                && Objects.equals(endTime, that.endTime)
                && Objects.equals(duration, that.duration)
                && Objects.equals(progress, that.progress)
                && Objects.equals(progressRate, that.progressRate)
                && Objects.equals(vanGp, that.vanGp)
                && Objects.equals(completed, that.completed);
    }

    // hashCode method (calls super)
    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), operatorName, workDate, startTime, endTime, duration, progress, progressRate, vanGp, completed);
    }

    // toString method (calls super)
    @Override
    public String toString() {
        return "ActualWork{"
                + "operatorName='" + operatorName + '\''
                + ", workDate=" + workDate
                + ", startTime=" + startTime
                + ", endTime=" + endTime
                + ", duration=" + duration
                + ", progress=" + progress
                + ", progressRate=" + progressRate
                + ", vanGp='" + vanGp + '\''
                + ", completed=" + completed
                + ", " + super.toString()
                + '}';
    }
}
