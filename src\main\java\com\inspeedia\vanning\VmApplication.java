package com.inspeedia.vanning;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.r2dbc.config.EnableR2dbcAuditing;
import org.springframework.web.reactive.config.EnableWebFlux;

/**
 * Main application class for VMA (Vanning Management Application) API
 *
 * This is a reactive Spring Boot application that provides REST APIs for
 * vanning management using WebFlux and R2DBC with MSSQL.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 */
@SpringBootApplication
@EnableWebFlux
@EnableR2dbcAuditing
public class VmApplication {

    public static void main(String[] args) {
        SpringApplication.run(VmApplication.class, args);
    }
}
