# Test Excel Import API
$baseUrl = "http://localhost:8080"
$excelFile = "C:\Dev\Mohan\Projects\MSS\Toyutsu\vanning\vma-api\src\main\resources\計画.xlsx"

# Check if Excel file exists
if (-not (Test-Path $excelFile)) {
    Write-Host "Excel file not found: $excelFile" -ForegroundColor Red
    exit 1
}

Write-Host "Testing Excel Import API..." -ForegroundColor Green
Write-Host "Excel file: $excelFile" -ForegroundColor Yellow

try {
    # Create multipart form data
    $boundary = [System.Guid]::NewGuid().ToString()
    $LF = "`r`n"
    
    # Read file content
    $fileBytes = [System.IO.File]::ReadAllBytes($excelFile)
    $fileEnc = [System.Text.Encoding]::GetEncoding('iso-8859-1').GetString($fileBytes)
    
    # Create multipart body
    $bodyLines = (
        "--$boundary",
        "Content-Disposition: form-data; name=`"file`"; filename=`"計画.xlsx`"",
        "Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet$LF",
        $fileEnc,
        "--$boundary--$LF"
    ) -join $LF
    
    # Make the request
    $response = Invoke-RestMethod -Uri "$baseUrl/api/planned-work/import" -Method Post -Body $bodyLines -ContentType "multipart/form-data; boundary=$boundary"
    
    Write-Host "Response:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 10
    
} catch {
    Write-Host "Error occurred:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body:" -ForegroundColor Yellow
        Write-Host $responseBody -ForegroundColor Yellow
    }
}
