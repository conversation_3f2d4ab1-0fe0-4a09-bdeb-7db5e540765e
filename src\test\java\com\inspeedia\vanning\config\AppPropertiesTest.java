package com.inspeedia.vanning.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.List;

/**
 * Unit tests for AppProperties configuration class
 */
class AppPropertiesTest {

    private AppProperties appProperties;
    
    @BeforeEach
    @SuppressWarnings("unused")
    void setup() {
        appProperties = new AppProperties();
    }

    @Test
    void testDefaultConstructor() {
        AppProperties props = new AppProperties();
        assertNotNull(props);
        assertNotNull(props.getCors());
        assertNotNull(props.getApi());
        assertNotNull(props.getPagination());
    }

    @Test
    void testParameterizedConstructor() {
        AppProperties.Cors cors = new AppProperties.Cors();
        AppProperties.Api api = new AppProperties.Api();
        AppProperties.Pagination pagination = new AppProperties.Pagination();

        AppProperties props = new AppProperties(cors, api, pagination);
        
        assertEquals(cors, props.getCors());
        assertEquals(api, props.getApi());
        assertEquals(pagination, props.getPagination());
    }

    @Test
    void testCorsConfiguration() {
        AppProperties.Cors cors = appProperties.getCors();
        
        List<String> origins = Arrays.asList("http://localhost:3000", "https://example.com");
        List<String> methods = Arrays.asList("GET", "POST", "PUT", "DELETE");
        List<String> headers = Arrays.asList("Content-Type", "Authorization");
        
        cors.setAllowedOrigins(origins);
        cors.setAllowedMethods(methods);
        cors.setAllowedHeaders(headers);
        cors.setAllowCredentials(true);
        cors.setMaxAge(3600);

        assertEquals(origins, cors.getAllowedOrigins());
        assertEquals(methods, cors.getAllowedMethods());
        assertEquals(headers, cors.getAllowedHeaders());
        assertTrue(cors.isAllowCredentials());
        assertEquals(3600, cors.getMaxAge());
    }

    @Test
    void testCorsConstructor() {
        List<String> origins = Arrays.asList("http://localhost:3000");
        List<String> methods = Arrays.asList("GET", "POST");
        List<String> headers = Arrays.asList("Content-Type");
        
        AppProperties.Cors cors = new AppProperties.Cors(origins, methods, headers, true, 7200);
        
        assertEquals(origins, cors.getAllowedOrigins());
        assertEquals(methods, cors.getAllowedMethods());
        assertEquals(headers, cors.getAllowedHeaders());
        assertTrue(cors.isAllowCredentials());
        assertEquals(7200, cors.getMaxAge());
    }

    @Test
    void testApiConfiguration() {
        AppProperties.Api api = appProperties.getApi();
        
        api.setVersion("v1");
        api.setBasePath("/api/v1");

        assertEquals("v1", api.getVersion());
        assertEquals("/api/v1", api.getBasePath());
    }

    @Test
    void testApiConstructor() {
        AppProperties.Api api = new AppProperties.Api("v2", "/api/v2");
        
        assertEquals("v2", api.getVersion());
        assertEquals("/api/v2", api.getBasePath());
    }

    @Test
    void testPaginationConfiguration() {
        AppProperties.Pagination pagination = appProperties.getPagination();
        
        // Test default values
        assertEquals(20, pagination.getDefaultPageSize());
        assertEquals(100, pagination.getMaxPageSize());
        
        // Test setters
        pagination.setDefaultPageSize(25);
        pagination.setMaxPageSize(200);

        assertEquals(25, pagination.getDefaultPageSize());
        assertEquals(200, pagination.getMaxPageSize());
    }

    @Test
    void testPaginationConstructor() {
        AppProperties.Pagination pagination = new AppProperties.Pagination(30, 150);
        
        assertEquals(30, pagination.getDefaultPageSize());
        assertEquals(150, pagination.getMaxPageSize());
    }

    @Test
    void testCorsEquals() {
        List<String> origins = Arrays.asList("http://localhost:3000");
        AppProperties.Cors cors1 = new AppProperties.Cors(origins, null, null, true, 3600);
        AppProperties.Cors cors2 = new AppProperties.Cors(origins, null, null, true, 3600);
        AppProperties.Cors cors3 = new AppProperties.Cors(origins, null, null, false, 3600);

        assertEquals(cors1, cors2);
        assertNotEquals(cors1, cors3);
    }

    @Test
    void testApiEquals() {
        AppProperties.Api api1 = new AppProperties.Api("v1", "/api/v1");
        AppProperties.Api api2 = new AppProperties.Api("v1", "/api/v1");
        AppProperties.Api api3 = new AppProperties.Api("v2", "/api/v1");

        assertEquals(api1, api2);
        assertNotEquals(api1, api3);
    }

    @Test
    void testPaginationEquals() {
        AppProperties.Pagination pagination1 = new AppProperties.Pagination(20, 100);
        AppProperties.Pagination pagination2 = new AppProperties.Pagination(20, 100);
        AppProperties.Pagination pagination3 = new AppProperties.Pagination(25, 100);

        assertEquals(pagination1, pagination2);
        assertNotEquals(pagination1, pagination3);
    }

    @Test
    void testHashCode() {
        AppProperties.Cors cors = new AppProperties.Cors();
        AppProperties.Api api = new AppProperties.Api();
        AppProperties.Pagination pagination = new AppProperties.Pagination();
        
        AppProperties props1 = new AppProperties(cors, api, pagination);
        AppProperties props2 = new AppProperties(cors, api, pagination);

        assertEquals(props1.hashCode(), props2.hashCode());
    }

    @Test
    void testToString() {
        String corsString = appProperties.getCors().toString();
        String apiString = appProperties.getApi().toString();
        String paginationString = appProperties.getPagination().toString();
        String propsString = appProperties.toString();
        
        assertNotNull(corsString);
        assertNotNull(apiString);
        assertNotNull(paginationString);
        assertNotNull(propsString);
        
        assertTrue(corsString.contains("Cors"));
        assertTrue(apiString.contains("Api"));
        assertTrue(paginationString.contains("Pagination"));
        assertTrue(propsString.contains("AppProperties"));
    }

    @Test
    void testNestedClassesIndependence() {
        // Test that nested classes work independently
        AppProperties.Cors cors = new AppProperties.Cors();
        AppProperties.Api api = new AppProperties.Api();
        AppProperties.Pagination pagination = new AppProperties.Pagination();
        
        cors.setAllowCredentials(true);
        api.setVersion("v1");
        pagination.setDefaultPageSize(50);
        
        // Changes to one shouldn't affect others
        assertTrue(cors.isAllowCredentials());
        assertEquals("v1", api.getVersion());
        assertEquals(50, pagination.getDefaultPageSize());
        
        // Other properties should remain at defaults
        assertNull(cors.getAllowedOrigins());
        assertNull(api.getBasePath());
        assertEquals(100, pagination.getMaxPageSize()); // default value
    }
}
