package com.inspeedia.vanning.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for Excel import settings This allows easy modification
 * of import parameters without code changes
 */
@Configuration
@ConfigurationProperties(prefix = "excel.import")
public class ExcelImportConfig {

    /**
     * Row index where headers are located (0-based)
     */
    private int headerRowIndex = 1; // Row 2 in Excel (0-based index)

    /**
     * Row index where data starts (0-based)
     */
    private int dataStartRowIndex = 2; // Row 3 in Excel (0-based index)

    /**
     * Default values for missing fields
     */
    private DefaultValues defaultValues = new DefaultValues();

    /**
     * Validation settings
     */
    private ValidationSettings validation = new ValidationSettings();

    /**
     * Character encoding settings
     */
    private EncodingSettings encoding = new EncodingSettings();

    // Get<PERSON> and Setters
    public int getHeaderRowIndex() {
        return headerRowIndex;
    }

    public void setHeaderRowIndex(int headerRowIndex) {
        this.headerRowIndex = headerRowIndex;
    }

    public int getDataStartRowIndex() {
        return dataStartRowIndex;
    }

    public void setDataStartRowIndex(int dataStartRowIndex) {
        this.dataStartRowIndex = dataStartRowIndex;
    }

    public DefaultValues getDefaultValues() {
        return defaultValues;
    }

    public void setDefaultValues(DefaultValues defaultValues) {
        this.defaultValues = defaultValues;
    }

    public ValidationSettings getValidation() {
        return validation;
    }

    public void setValidation(ValidationSettings validation) {
        this.validation = validation;
    }

    public EncodingSettings getEncoding() {
        return encoding;
    }

    public void setEncoding(EncodingSettings encoding) {
        this.encoding = encoding;
    }

    /**
     * Default values for missing fields
     */
    public static class DefaultValues {

        private String deliveryPlatform = "A";
        private String collectionPlatform = "B";
        private String size = "L1";

        public String getDeliveryPlatform() {
            return deliveryPlatform;
        }

        public void setDeliveryPlatform(String deliveryPlatform) {
            this.deliveryPlatform = deliveryPlatform;
        }

        public String getCollectionPlatform() {
            return collectionPlatform;
        }

        public void setCollectionPlatform(String collectionPlatform) {
            this.collectionPlatform = collectionPlatform;
        }

        public String getSize() {
            return size;
        }

        public void setSize(String size) {
            this.size = size;
        }
    }

    /**
     * Validation settings
     */
    public static class ValidationSettings {

        private String vanGpPattern = "^[A-Z0-9]{2}$";
        private int vanGpMinLength = 2;
        private int vanGpMaxLength = 2;

        public String getVanGpPattern() {
            return vanGpPattern;
        }

        public void setVanGpPattern(String vanGpPattern) {
            this.vanGpPattern = vanGpPattern;
        }

        public int getVanGpMinLength() {
            return vanGpMinLength;
        }

        public void setVanGpMinLength(int vanGpMinLength) {
            this.vanGpMinLength = vanGpMinLength;
        }

        public int getVanGpMaxLength() {
            return vanGpMaxLength;
        }

        public void setVanGpMaxLength(int vanGpMaxLength) {
            this.vanGpMaxLength = vanGpMaxLength;
        }
    }

    /**
     * Character encoding settings for Japanese text
     */
    public static class EncodingSettings {

        private String defaultCharset = "UTF-8";
        private boolean supportJapanese = true;

        public String getDefaultCharset() {
            return defaultCharset;
        }

        public void setDefaultCharset(String defaultCharset) {
            this.defaultCharset = defaultCharset;
        }

        public boolean isSupportJapanese() {
            return supportJapanese;
        }

        public void setSupportJapanese(boolean supportJapanese) {
            this.supportJapanese = supportJapanese;
        }
    }
}
