package com.inspeedia.vanning.service;

import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.domain.PlannedWork;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.time.LocalDate;
import java.time.LocalTime;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

/**
 * Unit tests for TaskService
 */
@ExtendWith(MockitoExtension.class)
class TaskServiceTest {

    @Mock
    private PlannedWorkService plannedWorkService;

    @Mock
    private ActualWorkService actualWorkService;

    @InjectMocks
    private TaskService taskService;

    private PlannedWork samplePlannedWork;
    private ActualWork sampleActualWork;

    @BeforeEach
    void setUp() {
        samplePlannedWork = new PlannedWork(
                LocalDate.of(2024, 1, 15),
                "User_AB_20240115",
                "AB",
                "C",
                "D",
                LocalTime.of(7, 30),
                "L1",
                LocalTime.of(8, 0),
                LocalTime.of(17, 0),
                LocalTime.of(9, 0)
        );
        samplePlannedWork.setId(1L);

        sampleActualWork = new ActualWork(
                "User_AB_20240115",
                LocalDate.of(2024, 1, 15),
                LocalTime.of(8, 15),
                LocalTime.of(17, 30),
                LocalTime.of(9, 15),
                85,
                85.0f,
                "AB",
                false
        );
        sampleActualWork.setId(1L);
    }

    @Test
    void testGetAllTasks() {
        when(plannedWorkService.getAllActivePlannedWork(anyInt(), anyInt()))
                .thenReturn(Flux.just(samplePlannedWork));
        when(actualWorkService.getAllActiveActualWork(anyInt(), anyInt()))
                .thenReturn(Flux.just(sampleActualWork));

        StepVerifier.create(taskService.getAllTasks())
                .expectNextMatches(task -> {
                    return task.getId().equals(1L)
                            && task.getName().equals("User_AB_20240115")
                            && task.getNo().equals("T001")
                            && task.getShippingDate().equals("2024-01-15")
                            && task.getVanGp().equals("AB")
                            && task.getDeliveryTime().equals("07:30")
                            && task.getPlannedStart().equals("08:00")
                            && task.getPlannedEnd().equals("17:00")
                            && task.getPlannedDuration().equals("9h")
                            && task.getActualStart().equals("08:15")
                            && task.getActualEnd().equals("17:30")
                            && task.getActualDuration().equals("9h 15m")
                            && task.getProgress().equals(85);
                })
                .verifyComplete();
    }

    @Test
    void testGetTodaysTasks() {
        when(plannedWorkService.getPlannedWorkForToday())
                .thenReturn(Flux.just(samplePlannedWork));
        when(actualWorkService.getActualWorkForCurrentDate())
                .thenReturn(Flux.just(sampleActualWork));

        StepVerifier.create(taskService.getTodayTasksReactive())
                .expectNextMatches(task -> {
                    return task.getId().equals(1L)
                            && task.getVanGp().equals("AB")
                            && task.getProgress().equals(85);
                })
                .verifyComplete();
    }

    @Test
    void testGetAllTasksWithoutActualWork() {
        when(plannedWorkService.getAllActivePlannedWork(anyInt(), anyInt()))
                .thenReturn(Flux.just(samplePlannedWork));
        when(actualWorkService.getAllActiveActualWork(anyInt(), anyInt()))
                .thenReturn(Flux.empty());

        StepVerifier.create(taskService.getAllTasks())
                .expectNextMatches(task -> {
                    return task.getId().equals(1L)
                            && task.getActualStart().equals("")
                            && task.getActualEnd().equals("")
                            && task.getActualDuration().equals("")
                            && task.getProgress().equals(0);
                })
                .verifyComplete();
    }

    @Test
    void testGetAllTasksEmpty() {
        when(plannedWorkService.getAllActivePlannedWork(anyInt(), anyInt()))
                .thenReturn(Flux.empty());
        when(actualWorkService.getAllActiveActualWork(anyInt(), anyInt()))
                .thenReturn(Flux.empty());

        StepVerifier.create(taskService.getAllTasks())
                .verifyComplete();
    }
}
