package com.inspeedia.vanning.exception;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * Unit tests for ErrorResponse and ValidationError classes
 */
class ErrorResponseTest {

    private ErrorResponse errorResponse;
    private LocalDateTime timestamp;

    @BeforeEach
    @SuppressWarnings("unused")
    void setUp() {
        timestamp = LocalDateTime.now();
        errorResponse = new ErrorResponse();
        errorResponse.setError("Bad Request");
        errorResponse.setMessage("Validation failed");
        errorResponse.setStatus(400);
        errorResponse.setPath("/api/test");
        errorResponse.setTimestamp(timestamp);
        errorResponse.setTraceId("trace-123");
    }

    @Test
    void testDefaultConstructor() {
        ErrorResponse response = new ErrorResponse();
        assertNotNull(response);
        assertNull(response.getError());
        assertNull(response.getMessage());
        assertEquals(0, response.getStatus());
        assertNull(response.getPath());
        assertNull(response.getTimestamp());
        assertNull(response.getTraceId());
        assertNull(response.getValidationErrors());
    }

    @Test
    void testParameterizedConstructor() {
        List<ErrorResponse.ValidationError> validationErrors = Arrays.asList(
            new ErrorResponse.ValidationError("field1", "value1", "error1")
        );
        
        ErrorResponse response = new ErrorResponse("Internal Error", "Server error", 500, 
                "/api/error", timestamp, "trace-456", validationErrors);
        
        assertEquals("Internal Error", response.getError());
        assertEquals("Server error", response.getMessage());
        assertEquals(500, response.getStatus());
        assertEquals("/api/error", response.getPath());
        assertEquals(timestamp, response.getTimestamp());
        assertEquals("trace-456", response.getTraceId());
        assertEquals(validationErrors, response.getValidationErrors());
    }

    @Test
    void testBuilder() {
        ErrorResponse response = ErrorResponse.builder()
                .error("Not Found")
                .message("Resource not found")
                .status(404)
                .path("/api/notfound")
                .timestamp(timestamp)
                .traceId("trace-789")
                .build();
        
        assertEquals("Not Found", response.getError());
        assertEquals("Resource not found", response.getMessage());
        assertEquals(404, response.getStatus());
        assertEquals("/api/notfound", response.getPath());
        assertEquals(timestamp, response.getTimestamp());
        assertEquals("trace-789", response.getTraceId());
    }

    @Test
    void testGettersAndSetters() {
        assertEquals("Bad Request", errorResponse.getError());
        assertEquals("Validation failed", errorResponse.getMessage());
        assertEquals(400, errorResponse.getStatus());
        assertEquals("/api/test", errorResponse.getPath());
        assertEquals(timestamp, errorResponse.getTimestamp());
        assertEquals("trace-123", errorResponse.getTraceId());

        // Test setters
        errorResponse.setError("Updated Error");
        errorResponse.setStatus(422);
        
        assertEquals("Updated Error", errorResponse.getError());
        assertEquals(422, errorResponse.getStatus());
    }

    @Test
    void testEquals() {
        ErrorResponse response1 = new ErrorResponse("Error", "Message", 400, "/path", timestamp, "trace", null);
        ErrorResponse response2 = new ErrorResponse("Error", "Message", 400, "/path", timestamp, "trace", null);
        ErrorResponse response3 = new ErrorResponse("Different", "Message", 400, "/path", timestamp, "trace", null);

        assertEquals(response1, response2);
        assertNotEquals(response1, response3);
        assertNotEquals(response1, null);
        assertNotEquals(response1, "string");
    }

    @Test
    void testHashCode() {
        ErrorResponse response1 = new ErrorResponse("Error", "Message", 400, "/path", timestamp, "trace", null);
        ErrorResponse response2 = new ErrorResponse("Error", "Message", 400, "/path", timestamp, "trace", null);

        assertEquals(response1.hashCode(), response2.hashCode());
    }

    @Test
    void testToString() {
        String toString = errorResponse.toString();
        
        assertNotNull(toString);
        assertTrue(toString.contains("ErrorResponse"));
        assertTrue(toString.contains("Bad Request"));
        assertTrue(toString.contains("Validation failed"));
        assertTrue(toString.contains("status=400"));
        assertTrue(toString.contains("trace-123"));
    }

    @Test
    void testValidationError() {
        ErrorResponse.ValidationError validationError = new ErrorResponse.ValidationError();
        validationError.setField("userName");
        validationError.setRejectedValue("");
        validationError.setMessage("User name is required");

        assertEquals("userName", validationError.getField());
        assertEquals("", validationError.getRejectedValue());
        assertEquals("User name is required", validationError.getMessage());
    }

    @Test
    void testValidationErrorConstructor() {
        ErrorResponse.ValidationError validationError = new ErrorResponse.ValidationError("email", "invalid", "Invalid email format");

        assertEquals("email", validationError.getField());
        assertEquals("invalid", validationError.getRejectedValue());
        assertEquals("Invalid email format", validationError.getMessage());
    }

    @Test
    void testValidationErrorBuilder() {
        ErrorResponse.ValidationError validationError = ErrorResponse.ValidationError.builder()
                .field("progress")
                .rejectedValue(15)
                .message("Progress must be between 0 and 10")
                .build();

        assertEquals("progress", validationError.getField());
        assertEquals(15, validationError.getRejectedValue());
        assertEquals("Progress must be between 0 and 10", validationError.getMessage());
    }

    @Test
    void testValidationErrorEquals() {
        ErrorResponse.ValidationError error1 = new ErrorResponse.ValidationError("field", "value", "message");
        ErrorResponse.ValidationError error2 = new ErrorResponse.ValidationError("field", "value", "message");
        ErrorResponse.ValidationError error3 = new ErrorResponse.ValidationError("different", "value", "message");

        assertEquals(error1, error2);
        assertNotEquals(error1, error3);
        assertNotEquals(error1, null);
    }

    @Test
    void testValidationErrorHashCode() {
        ErrorResponse.ValidationError error1 = new ErrorResponse.ValidationError("field", "value", "message");
        ErrorResponse.ValidationError error2 = new ErrorResponse.ValidationError("field", "value", "message");

        assertEquals(error1.hashCode(), error2.hashCode());
    }

    @Test
    void testValidationErrorToString() {
        ErrorResponse.ValidationError validationError = new ErrorResponse.ValidationError("field", "value", "message");
        String toString = validationError.toString();
        
        assertNotNull(toString);
        assertTrue(toString.contains("ValidationError"));
        assertTrue(toString.contains("field='field'"));
        assertTrue(toString.contains("rejectedValue=value"));
        assertTrue(toString.contains("message='message'"));
    }
}
