-- Migration script for VMA database schema changes
-- This script handles the migration from old schema to new schema
-- Each statement is kept simple to avoid parsing issues

-- Rename user_name to operator_name in actual_work table if it exists
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'user_name')
    EXEC sp_rename 'actual_work.user_name', 'operator_name', 'COLUMN';

-- Rename date to work_date in actual_work table if it exists
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'date')
    EXEC sp_rename 'actual_work.date', 'work_date', 'COLUMN';

-- Add van_gp column to actual_work table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'van_gp')
    ALTER TABLE actual_work ADD van_gp NVARCHAR(2) NOT NULL DEFAULT 'AB' CHECK (LEN(van_gp) = 2 AND van_gp = UPPER(van_gp));

-- Add completed column to actual_work table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('actual_work') AND name = 'completed')
    ALTER TABLE actual_work ADD completed BIT DEFAULT 0;

-- Rename date to work_date in planned_work table if it exists
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'date')
    EXEC sp_rename 'planned_work.date', 'work_date', 'COLUMN';

-- Add operator_name column to planned_work table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('planned_work') AND name = 'operator_name')
    ALTER TABLE planned_work ADD operator_name NVARCHAR(100) NOT NULL DEFAULT 'Unknown';
