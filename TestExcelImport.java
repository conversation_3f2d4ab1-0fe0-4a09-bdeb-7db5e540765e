
import java.io.*;
import java.net.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;

public class TestExcelImport {

    public static void main(String[] args) {
        String baseUrl = "http://localhost:8080";
        String excelFile = "src/main/resources/計画.xlsx";

        try {
            // Create credentials for basic authentication
            String username = "admin";
            String password = "admin123";
            String auth = username + ":" + password;
            String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());

            // Read Excel file
            byte[] fileBytes = Files.readAllBytes(Paths.get(excelFile));

            // Create connection
            URL url = new URL(baseUrl + "/api/planned-work/import");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setRequestProperty("Authorization", "Basic " + encodedAuth);

            // Create multipart form data
            String boundary = "----WebKitFormBoundary" + System.currentTimeMillis();
            connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

            try (OutputStream os = connection.getOutputStream(); PrintWriter writer = new PrintWriter(new OutputStreamWriter(os, "UTF-8"), true)) {

                // Write file part
                writer.append("--" + boundary).append("\r\n");
                writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"test-plan.xlsx\"").append("\r\n");
                writer.append("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet").append("\r\n");
                writer.append("\r\n");
                writer.flush();

                os.write(fileBytes);
                os.flush();

                writer.append("\r\n");
                writer.append("--" + boundary + "--").append("\r\n");
            }

            // Get response
            int responseCode = connection.getResponseCode();
            System.out.println("Response Code: " + responseCode);

            // Read response
            InputStream inputStream = responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream();
            if (inputStream != null) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                    String line;
                    System.out.println("Response Body:");
                    while ((line = reader.readLine()) != null) {
                        System.out.println(line);
                    }
                }
            }

        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
