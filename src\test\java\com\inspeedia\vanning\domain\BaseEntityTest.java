package com.inspeedia.vanning.domain;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;

/**
 * Unit tests for BaseEntity
 */
class BaseEntityTest {

    private TestEntity entity;
    private LocalDateTime now;

    @BeforeEach
    @SuppressWarnings("unused")
    void setUp() {
        now = LocalDateTime.now();
        entity = new TestEntity();
        entity.setId(1L);
        entity.setCreatedAt(now);
        entity.setUpdatedAt(now);
        entity.setVersion(1L);
        entity.setCreatedBy("admin");
        entity.setUpdatedBy("admin");
        entity.setDeleted(false);
    }

    @Test
    void testDefaultConstructor() {
        TestEntity testEntity = new TestEntity();
        assertNotNull(testEntity);
        assertNull(testEntity.getId());
        assertNull(testEntity.getCreatedAt());
        assertNull(testEntity.getUpdatedAt());
        assertNull(testEntity.getVersion());
        assertNull(testEntity.getCreatedBy());
        assertNull(testEntity.getUpdatedBy());
        assertFalse(testEntity.isDeleted()); // default value
    }

    @Test
    void testParameterizedConstructor() {
        TestEntity testEntity = new TestEntity(2L, now, now, 2L, "user", "user", true);
        
        assertEquals(2L, testEntity.getId());
        assertEquals(now, testEntity.getCreatedAt());
        assertEquals(now, testEntity.getUpdatedAt());
        assertEquals(2L, testEntity.getVersion());
        assertEquals("user", testEntity.getCreatedBy());
        assertEquals("user", testEntity.getUpdatedBy());
        assertTrue(testEntity.isDeleted());
    }

    @Test
    void testGettersAndSetters() {
        assertEquals(1L, entity.getId());
        assertEquals(now, entity.getCreatedAt());
        assertEquals(now, entity.getUpdatedAt());
        assertEquals(1L, entity.getVersion());
        assertEquals("admin", entity.getCreatedBy());
        assertEquals("admin", entity.getUpdatedBy());
        assertFalse(entity.isDeleted());

        // Test setters
        LocalDateTime newTime = LocalDateTime.now().plusHours(1);
        entity.setId(2L);
        entity.setCreatedAt(newTime);
        entity.setUpdatedAt(newTime);
        entity.setVersion(2L);
        entity.setCreatedBy("newUser");
        entity.setUpdatedBy("newUser");
        entity.setDeleted(true);

        assertEquals(2L, entity.getId());
        assertEquals(newTime, entity.getCreatedAt());
        assertEquals(newTime, entity.getUpdatedAt());
        assertEquals(2L, entity.getVersion());
        assertEquals("newUser", entity.getCreatedBy());
        assertEquals("newUser", entity.getUpdatedBy());
        assertTrue(entity.isDeleted());
    }

    @Test
    void testEquals() {
        TestEntity entity1 = new TestEntity(1L, now, now, 1L, "admin", "admin", false);
        TestEntity entity2 = new TestEntity(1L, now, now, 1L, "admin", "admin", false);
        TestEntity entity3 = new TestEntity(2L, now, now, 1L, "admin", "admin", false);

        assertEquals(entity1, entity2);
        assertNotEquals(entity1, entity3);
        assertNotEquals(entity1, null);
        assertNotEquals(entity1, "string");
    }

    @Test
    void testHashCode() {
        TestEntity entity1 = new TestEntity(1L, now, now, 1L, "admin", "admin", false);
        TestEntity entity2 = new TestEntity(1L, now, now, 1L, "admin", "admin", false);

        assertEquals(entity1.hashCode(), entity2.hashCode());
    }

    @Test
    void testToString() {
        String toString = entity.toString();
        
        assertNotNull(toString);
        assertTrue(toString.contains("BaseEntity"));
        assertTrue(toString.contains("id=1"));
        assertTrue(toString.contains("createdBy='admin'"));
        assertTrue(toString.contains("updatedBy='admin'"));
        assertTrue(toString.contains("deleted=false"));
    }

    // Test implementation of BaseEntity for testing purposes
    private static class TestEntity extends BaseEntity {
        
        public TestEntity() {
            super();
        }
        
        public TestEntity(Long id, LocalDateTime createdAt, LocalDateTime updatedAt, Long version,
                         String createdBy, String updatedBy, boolean deleted) {
            super(id, createdAt, updatedAt, version, createdBy, updatedBy, deleted);
        }
    }
}
