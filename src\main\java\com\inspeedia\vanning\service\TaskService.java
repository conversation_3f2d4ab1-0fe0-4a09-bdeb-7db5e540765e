package com.inspeedia.vanning.service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.inspeedia.vanning.domain.ActualWork;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.dto.TaskDto;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/**
 * Service for Task operations
 *
 * This service combines PlannedWork and ActualWork data to provide a unified
 * Task view for the frontend dashboard.
 */
@Service
public class TaskService {

    private final Logger log = LoggerFactory.getLogger(TaskService.class);
    private final PlannedWorkService plannedWorkService;
    private final ActualWorkService actualWorkService;

    public TaskService(PlannedWorkService plannedWorkService, ActualWorkService actualWorkService) {
        this.plannedWorkService = plannedWorkService;
        this.actualWorkService = actualWorkService;
    }

    /**
     * Get all tasks by combining planned and actual work data
     */
    public Flux<TaskDto> getAllTasks() {
        log.debug("Fetching all tasks by combining planned and actual work");

        return plannedWorkService.getAllActivePlannedWork(0, 100)
                .collectList()
                .flatMapMany(plannedWorks -> {
                    // Get all actual work records
                    return actualWorkService.getAllActiveActualWork(0, 100)
                            .collectList()
                            .flatMapMany(actualWorks -> {
                                // Create a map of actual work by user name for quick lookup
                                Map<String, ActualWork> actualWorkMap = actualWorks.stream()
                                        .collect(Collectors.toMap(
                                                ActualWork::getOperatorName,
                                                work -> work,
                                                (existing, replacement) -> replacement // Keep the latest if duplicates
                                        ));

                                // Convert planned works to tasks
                                return Flux.fromIterable(plannedWorks)
                                        .map(plannedWork -> convertToTaskDto(plannedWork, actualWorkMap.get(generateUserName(plannedWork))));
                            });
                });
    }

    public Flux<TaskDto> getTodayTasksReactive() {
        log.debug("Fetching today's tasks");

        Mono<Map<String, ActualWork>> actualMapMono
                = actualWorkService.getActualWorkForCurrentDate()
                        .groupBy(this::buildKey)
                        .flatMap(grouped
                                -> grouped
                                .reduce((a1, a2) -> {
                                    if (a1.getStartTime() == null) {
                                        return a2;
                                    }
                                    if (a2.getStartTime() == null) {
                                        return a1;
                                    }
                                    return a1.getStartTime().isAfter(a2.getStartTime()) ? a1 : a2;
                                })
                                .map(latest -> Tuples.of(grouped.key(), latest))
                        )
                        .collectMap(Tuple2::getT1, Tuple2::getT2)
                        .cache(); // Cache the result so it can be reused multiple times

        return plannedWorkService.getPlannedWorkForToday()
                .flatMap(planned
                        -> actualMapMono.map(actualMap -> {
                    ActualWork match = actualMap.get(buildKey(planned));
                    return convertToTaskDto(planned, match);
                })
                );
    }

    public Flux<TaskDto> getTodayTasksForOperatorReactive(String operatorName) {
        log.debug("Fetching today's tasks the operator: {}", operatorName);

        // Get planned work and actual work in parallel
        Flux<PlannedWork> plannedWorkFlux = plannedWorkService.getPlannedWorkForOperatorFromToday(operatorName);
        Flux<ActualWork> actualWorkFlux = actualWorkService.getActualWorkByUserNameAndWorkDate(operatorName, LocalDate.now());

        // Collect actual work into a map first
        return actualWorkFlux
                .groupBy(this::buildKey)
                .flatMap(grouped
                        -> grouped
                        .reduce((a1, a2) -> {
                            if (a1.getStartTime() == null) {
                                return a2;
                            }
                            if (a2.getStartTime() == null) {
                                return a1;
                            }
                            return a1.getStartTime().isAfter(a2.getStartTime()) ? a1 : a2;
                        })
                        .map(latest -> Tuples.of(grouped.key(), latest))
                )
                .collectMap(Tuple2::getT1, Tuple2::getT2)
                .defaultIfEmpty(Map.of())
                .doOnSuccess(map -> log.debug("Actual work map created: {}", map.size()))
                .doOnError(error -> log.error("Error creating actual work map: {}", error.getMessage()))
                .flatMapMany(actualMap
                        -> plannedWorkFlux.map(planned -> {
                    ActualWork match = actualMap.get(buildKey(planned));
                    return convertToTaskDto(planned, match);
                })
                )
                .doOnComplete(() -> log.debug("Successfully fetched today's tasks for operator: {}", operatorName))
                .doOnError(error -> log.error("Error fetching today's tasks for operator: {}", error.getMessage()));
    }

    /**
     * Builds a composite key for matching PlannedWork <-> ActualWork
     */
    private String buildKey(PlannedWork planned) {
        return planned.getOperatorName() + "|" + planned.getWorkDate() + "|" + planned.getVanGp();
    }

    private String buildKey(ActualWork actual) {
        return actual.getOperatorName() + "|" + actual.getWorkDate() + "|" + actual.getVanGp();
    }

    /**
     * Convert PlannedWork and ActualWork to TaskDto
     */
    private TaskDto convertToTaskDto(PlannedWork plannedWork, ActualWork actualWork) {
        TaskDto task = new TaskDto();

        task.setId(plannedWork.getId());
        task.setName(plannedWork.getOperatorName());
        task.setNo(generateTaskNumber(plannedWork));
        task.setShippingDate(plannedWork.getWorkDate().toString());
        task.setVanGp(plannedWork.getVanGp());
        task.setDeliveryTime(formatTime(plannedWork.getLoadTime()));
        task.setPlannedStart(formatTime(plannedWork.getStartTime()));
        task.setPlannedEnd(formatTime(plannedWork.getEndTime()));
        task.setPlannedDuration(formatDuration(plannedWork.getDuration()));

        // Set actual work data if available
        if (actualWork != null) {
            task.setActualStart(formatTime(actualWork.getStartTime()));
            task.setActualEnd(formatTime(actualWork.getEndTime()));
            task.setActualDuration(formatDuration(actualWork.getDuration()));
            task.setProgress(actualWork.getProgress() != null ? actualWork.getProgress() : 0);
        } else {
            task.setActualStart("");
            task.setActualEnd("");
            task.setActualDuration("");
            task.setProgress(0);
        }

        return task;
    }

    /**
     * Generate a task number based on planned work
     */
    private String generateTaskNumber(PlannedWork plannedWork) {
        return String.format("T%03d", plannedWork.getId());
    }

    /**
     * Generate a user name based on planned work (for matching with actual
     * work)
     */
    private String generateUserName(PlannedWork plannedWork) {
        return String.format("User_%s_%s",
                plannedWork.getVanGp(),
                plannedWork.getWorkDate().toString().replace("-", ""));
    }

    /**
     * Format LocalTime to string (HH:mm format)
     */
    private String formatTime(LocalTime time) {
        if (time == null) {
            return "";
        }
        return time.format(DateTimeFormatter.ofPattern("HH:mm"));
    }

    /**
     * Format duration LocalTime to readable string
     */
    private String formatDuration(LocalTime duration) {
        if (duration == null) {
            return "";
        }
        int hours = duration.getHour();
        int minutes = duration.getMinute();

        if (minutes == 0) {
            return hours + "h";
        } else {
            return String.format("%dh %dm", hours, minutes);
        }
    }
}
