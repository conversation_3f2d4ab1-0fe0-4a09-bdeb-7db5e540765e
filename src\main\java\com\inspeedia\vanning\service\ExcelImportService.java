package com.inspeedia.vanning.service;

import com.inspeedia.vanning.config.ExcelColumnMapping;
import com.inspeedia.vanning.config.ExcelImportConfig;
import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.dto.ImportResultDto;
import com.inspeedia.vanning.repository.PlannedWorkRepository;
import com.inspeedia.vanning.service.excel.ExcelStreamingReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.inspeedia.vanning.dto.RowDto;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import java.io.ByteArrayInputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Service for importing planned work data from Excel files
 * This service handles Excel file processing, validation, and database
 * operations for importing planned work records with proper error handling.
 */
@Service
public class ExcelImportService {

	private final Logger log = LoggerFactory.getLogger(ExcelImportService.class);
	private final PlannedWorkRepository plannedWorkRepository;
	private final Validator validator;
	private final ExcelImportConfig config;
	private final ExcelStreamingReader excelStreamingReader;

	public ExcelImportService(PlannedWorkRepository plannedWorkRepository, Validator validator
		, ExcelImportConfig config, ExcelStreamingReader excelStreamingReader) {
		this.plannedWorkRepository = plannedWorkRepository;
		this.validator = validator;
		this.config = config;
		this.excelStreamingReader = excelStreamingReader;
	}

	/**
	 * Import planned work data from Excel file
	 */
	public Mono<ImportResultDto> importPlannedWorkFromExcel(FilePart filePart) {
		log.info("Starting Excel import for file: {}", filePart.filename());
		// Delegate to streaming implementation to keep public API stable
		return importPlannedWork(filePart)
				.doOnSuccess(result -> log.info("Excel import completed: {}", result))
				.doOnError(error -> log.error("Excel import failed: {}", error.getMessage()));
	}

	/**
	 * Stream rows from Excel using Apache POI SAX-based API.
	 */
	public Flux<RowDto> importExcelStreaming(FilePart filePart) {
		return DataBufferUtils.join(filePart.content())
				.flatMapMany(dataBuffer -> {
					byte[] bytes = new byte[dataBuffer.readableByteCount()];
					dataBuffer.read(bytes);
					DataBufferUtils.release(dataBuffer);

					return Flux.using(
							() -> new ByteArrayInputStream(bytes),
							excelStreamingReader::read,
							is -> { try { is.close(); } catch (Exception ignore) {} }
					);
				});
	}

	/**
	 * Streaming import that maps rows to PlannedWork and saves with per-row error capture.
	 */
	public Mono<ImportResultDto> importPlannedWork(FilePart filePart) {
		List<String> errors = new ArrayList<>();
		AtomicInteger total = new AtomicInteger(0);
		AtomicInteger saved = new AtomicInteger(0);

		return importExcelStreaming(filePart)
				.skip(2) // skip headers (two rows)
				.filter(this::isEmptyRowStreamingNegated)
				.map(this::toPlannedWork)
				.flatMap(result -> {
					if (!result.isValid) {
						errors.add("Sheet '" + result.sheetName + "' Row " + result.rowNum + ": " + result.errorMessage);
						return Mono.empty();
					}
					total.incrementAndGet();
					return Mono.just(new RowEntity(result.rowNum, result.sheetName, result.entity));
				})
				.buffer(100)
				.flatMap(batch -> Flux.fromIterable(batch)
						.flatMap(re -> plannedWorkRepository
								.findByWorkDateAndVanGpAndOperatorName(
										re.entity.getWorkDate(),
										re.entity.getVanGp(),
										re.entity.getOperatorName()
								)
								.next()
								.flatMap(existing -> {
									existing.setLoadTime(re.entity.getLoadTime());
									existing.setSize(re.entity.getSize());
									existing.setStartTime(re.entity.getStartTime());
									existing.setEndTime(re.entity.getEndTime());
									existing.setDuration(re.entity.getDuration());
									existing.setUpdatedAt(LocalDateTime.now());
									existing.setUpdatedBy("excel-import");
									return plannedWorkRepository.save(existing);
								})
								.switchIfEmpty(plannedWorkRepository.save(re.entity))
								.doOnSuccess(pw -> saved.incrementAndGet())
								.onErrorResume(e -> {
									errors.add("Sheet '" + re.sheetName + "' Row " + re.rowNum + ": " + mapDbError(e));
									return Mono.empty();
								})
								, 16)
						.then(Mono.empty())
				)
				.then(Mono.defer(() -> {
					int failed = errors.size();
					ImportResultDto dto = new ImportResultDto(total.get(), saved.get(), 0, failed, errors, List.of(),
							String.format("Import completed: %d saved, %d failed", saved.get(), failed));
					return Mono.just(dto);
				}))
				.doOnError(e -> log.error("Streaming import failed: {}", e.getMessage()));
	}

	private boolean isEmptyRowStreamingNegated(RowDto row) {
		if (row == null || row.cells() == null) {
			return false;
		}
		for (String c : row.cells()) {
			if (c != null && !c.trim().isEmpty()) {
				return true;
			}
		}
		return false;
	}

	/**
	 * Parse Excel row to PlannedWork entity
	 */
	private RowMappingResult toPlannedWork(RowDto row) {
		try {
			// Map using ExcelColumnMapping indices assuming same positions as workbook mode
			PlannedWork plannedWork = new PlannedWork();
			String dateStr = safeCell(row, ExcelColumnMapping.DATE.getColumnIndex());
			plannedWork.setWorkDate(parseDate(dateStr, "Date"));

			String vanGp = safeCell(row, ExcelColumnMapping.VAN_GP.getColumnIndex()).trim().toUpperCase();
			if (vanGp.isEmpty() || vanGp.equals("0")) {
				throw new IllegalArgumentException("Van GP is required");
			}
			if (!vanGp.matches(config.getValidation().getVanGpPattern())) {
				throw new IllegalArgumentException("Van GP must match pattern: " + config.getValidation().getVanGpPattern());
			}
			plannedWork.setVanGp(vanGp);

			plannedWork.setOperatorName(row.sheetName());
			plannedWork.setDeliveryPlatform(config.getDefaultValues().getDeliveryPlatform());
			plannedWork.setCollectionPlatform(config.getDefaultValues().getCollectionPlatform());
			plannedWork.setSize(config.getDefaultValues().getSize());

			String loadTimeStr = safeCell(row, ExcelColumnMapping.LOAD_TIME.getColumnIndex());
			plannedWork.setLoadTime(parseTime(loadTimeStr, "Load Time"));

			String startTimeStr = safeCell(row, ExcelColumnMapping.START_TIME.getColumnIndex());
			plannedWork.setStartTime(parseTime(startTimeStr, "Start Time"));

			String endTimeStr = safeCell(row, ExcelColumnMapping.END_TIME.getColumnIndex());
			plannedWork.setEndTime(parseTime(endTimeStr, "End Time"));

			String durationStr = safeCell(row, ExcelColumnMapping.WORK_TIME.getColumnIndex());
			plannedWork.setDuration(parseTime(durationStr, "Duration"));

			LocalDateTime now = LocalDateTime.now();
			plannedWork.setCreatedAt(now);
			plannedWork.setUpdatedAt(now);
			plannedWork.setCreatedBy("excel-import");
			plannedWork.setUpdatedBy("excel-import");

			Set<ConstraintViolation<PlannedWork>> violations = validator.validate(plannedWork);
			if (!violations.isEmpty()) {
				StringBuilder sb = new StringBuilder();
				for (ConstraintViolation<PlannedWork> v : violations) {
					sb.append(v.getMessage()).append("; ");
				}
				return RowMappingResult.invalid(row.rowNum(), row.sheetName(), sb.toString());
			}

			return RowMappingResult.valid(row.rowNum(), row.sheetName(), plannedWork);
		} catch (Exception e) {
			return RowMappingResult.invalid(row.rowNum(), row.sheetName(), e.getMessage());
		}
	}

	private String safeCell(RowDto row, int index) {
		List<String> cells = row.cells();
		if (index < 0 || index >= cells.size()) {
			return "";
		}
		return cells.get(index) == null ? "" : cells.get(index);
	}

	private record RowEntity(int rowNum, String sheetName, PlannedWork entity) {}

	private static class RowMappingResult {
		final int rowNum;
		final String sheetName;
		final boolean isValid;
		final PlannedWork entity;
		final String errorMessage;

		private RowMappingResult(int rowNum, String sheetName, boolean isValid, PlannedWork entity, String errorMessage) {
			this.rowNum = rowNum;
			this.sheetName = sheetName;
			this.isValid = isValid;
			this.entity = entity;
			this.errorMessage = errorMessage;
		}

		static RowMappingResult valid(int rowNum, String sheetName, PlannedWork entity) {
			return new RowMappingResult(rowNum, sheetName, true, entity, null);
		}

		static RowMappingResult invalid(int rowNum, String sheetName, String errorMessage) {
			return new RowMappingResult(rowNum, sheetName, false, null, errorMessage);
		}
	}

	/**
	 * Parse date string to LocalDate
	 */
	private LocalDate parseDate(String dateStr, String fieldName) {
		if (dateStr == null || dateStr.trim().isEmpty()) {
			throw new IllegalArgumentException(fieldName + " is required");
		}

		try {
			// Try different date formats
			DateTimeFormatter[] formatters = {
				DateTimeFormatter.ofPattern("yyyy-MM-dd"),
				DateTimeFormatter.ofPattern("dd/MM/yyyy"),
				DateTimeFormatter.ofPattern("MM/dd/yyyy"),
				DateTimeFormatter.ofPattern("dd-MM-yyyy")
			};

			for (DateTimeFormatter formatter : formatters) {
				try {
					return LocalDate.parse(dateStr.trim(), formatter);
				} catch (DateTimeParseException ignored) {
					// Try next format
				}
			}

			throw new IllegalArgumentException("Invalid date format: " + dateStr);
		} catch (Exception e) {
			throw new IllegalArgumentException("Invalid " + fieldName + " format: " + dateStr);
		}
	}

	/**
	 * Parse time string to LocalTime
	 */
	private LocalTime parseTime(String timeStr, String fieldName) {
		if (timeStr == null || timeStr.trim().isEmpty()) {
			throw new IllegalArgumentException(fieldName + " is required");
		}

		try {
			// Try different time formats
			DateTimeFormatter[] formatters = {
				DateTimeFormatter.ofPattern("HH:mm:ss"),
				DateTimeFormatter.ofPattern("HH:mm"),
				DateTimeFormatter.ofPattern("H:mm"),
				DateTimeFormatter.ofPattern("H:mm:ss")
			};

			for (DateTimeFormatter formatter : formatters) {
				try {
					return LocalTime.parse(timeStr.trim(), formatter);
				} catch (DateTimeParseException ignored) {
					// Try next format
				}
			}

			throw new IllegalArgumentException("Invalid time format: " + timeStr);
		} catch (Exception e) {
			throw new IllegalArgumentException("Invalid " + fieldName + " format: " + timeStr);
		}
	}

	private String mapDbError(Throwable e) {
		String msg = e.getMessage() == null ? "Unknown database error" : e.getMessage();
		// Provide concise, user-friendly messages; could inspect vendor codes if needed
		if (e instanceof org.springframework.dao.DataIntegrityViolationException) {
			return "Constraint violation while saving record: " + msg;
		}
		if (e instanceof io.r2dbc.spi.R2dbcTransientResourceException) {
			return "Transient database error (retryable): " + msg;
		}
		if (e instanceof io.r2dbc.spi.R2dbcTimeoutException) {
			return "Database operation timed out: " + msg;
		}
		return "Database error: " + msg;
	}
}
