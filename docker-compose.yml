version: "3.8"

services:
  mssql:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: vma-mssql
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourPassword123!
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - mssql_data:/var/opt/mssql
    networks:
      - vma-network
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourPassword123! -Q 'SELECT 1'",
        ]
      interval: 30s
      timeout: 10s
      retries: 5

  vma-api:
    build: .
    container_name: vma-api
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DB_HOST=mssql
      - DB_PORT=1433
      - DB_NAME=vma_db
      - DB_USERNAME=sa
      - DB_PASSWORD=YourPassword123!
    ports:
      - "8080:8080"
    depends_on:
      mssql:
        condition: service_healthy
    networks:
      - vma-network
    restart: unless-stopped

volumes:
  mssql_data:

networks:
  vma-network:
    driver: bridge
