# Test Excel Import API with detailed error reporting
$baseUrl = "http://localhost:8080"
$excelFile = "test-plan.xlsx"

Write-Host "Testing Excel Import API with detailed error reporting..." -ForegroundColor Green

# Create credentials for basic authentication
$username = "admin"
$password = "admin123"
$base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("{0}:{1}" -f $username, $password)))

try {
    # Use System.Net.WebClient for more control
    $webClient = New-Object System.Net.WebClient
    $webClient.Headers.Add("Authorization", "Basic $base64AuthInfo")
    
    # Read file as bytes
    $fileBytes = [System.IO.File]::ReadAllBytes($excelFile)
    
    # Create boundary and multipart content
    $boundary = [System.Guid]::NewGuid().ToString()
    $contentType = "multipart/form-data; boundary=$boundary"
    
    # Create multipart body
    $encoding = [System.Text.Encoding]::UTF8
    $bodyBuilder = New-Object System.Text.StringBuilder
    
    $bodyBuilder.AppendLine("--$boundary") | Out-Null
    $bodyBuilder.AppendLine('Content-Disposition: form-data; name="file"; filename="test-plan.xlsx"') | Out-Null
    $bodyBuilder.AppendLine('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') | Out-Null
    $bodyBuilder.AppendLine() | Out-Null
    
    # Convert to bytes and combine with file bytes
    $headerBytes = $encoding.GetBytes($bodyBuilder.ToString())
    $footerBytes = $encoding.GetBytes("`r`n--$boundary--`r`n")
    
    # Combine all bytes
    $totalBytes = New-Object byte[] ($headerBytes.Length + $fileBytes.Length + $footerBytes.Length)
    [Array]::Copy($headerBytes, 0, $totalBytes, 0, $headerBytes.Length)
    [Array]::Copy($fileBytes, 0, $totalBytes, $headerBytes.Length, $fileBytes.Length)
    [Array]::Copy($footerBytes, 0, $totalBytes, $headerBytes.Length + $fileBytes.Length, $footerBytes.Length)
    
    $webClient.Headers.Add("Content-Type", $contentType)
    
    Write-Host "Sending request..." -ForegroundColor Yellow
    $response = $webClient.UploadData("$baseUrl/api/planned-work/import", "POST", $totalBytes)
    $responseText = $encoding.GetString($response)
    
    Write-Host "Success!" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Green
    Write-Host $responseText -ForegroundColor White
    
} catch [System.Net.WebException] {
    Write-Host "WebException occurred:" -ForegroundColor Red
    Write-Host "Status: $($_.Exception.Status)" -ForegroundColor Yellow
    
    if ($_.Exception.Response) {
        $responseStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($responseStream)
        $errorResponse = $reader.ReadToEnd()
        
        Write-Host "HTTP Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
        Write-Host "Error Response:" -ForegroundColor Yellow
        Write-Host $errorResponse -ForegroundColor White
    }
} catch {
    Write-Host "General Exception occurred:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host $_.Exception.GetType().FullName -ForegroundColor Yellow
} finally {
    if ($webClient) {
        $webClient.Dispose()
    }
}
