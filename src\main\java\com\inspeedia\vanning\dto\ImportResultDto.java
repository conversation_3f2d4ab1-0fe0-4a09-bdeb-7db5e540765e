package com.inspeedia.vanning.dto;

import java.util.List;

/**
 * DTO for Excel import results
 * 
 * This DTO contains the results of importing planned work data from Excel,
 * including success counts, error details, and validation messages.
 */
public class ImportResultDto {

    private int totalRecords;
    private int successfulInserts;
    private int successfulUpdates;
    private int failedRecords;
    private List<String> errors;
    private List<String> warnings;
    private String message;

    // Default constructor
    public ImportResultDto() {
    }

    // Constructor with all fields
    public ImportResultDto(int totalRecords, int successfulInserts, int successfulUpdates, 
                          int failedRecords, List<String> errors, List<String> warnings, String message) {
        this.totalRecords = totalRecords;
        this.successfulInserts = successfulInserts;
        this.successfulUpdates = successfulUpdates;
        this.failedRecords = failedRecords;
        this.errors = errors;
        this.warnings = warnings;
        this.message = message;
    }

    // Getters and setters
    public int getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(int totalRecords) {
        this.totalRecords = totalRecords;
    }

    public int getSuccessfulInserts() {
        return successfulInserts;
    }

    public void setSuccessfulInserts(int successfulInserts) {
        this.successfulInserts = successfulInserts;
    }

    public int getSuccessfulUpdates() {
        return successfulUpdates;
    }

    public void setSuccessfulUpdates(int successfulUpdates) {
        this.successfulUpdates = successfulUpdates;
    }

    public int getFailedRecords() {
        return failedRecords;
    }

    public void setFailedRecords(int failedRecords) {
        this.failedRecords = failedRecords;
    }

    public List<String> getErrors() {
        return errors;
    }

    public void setErrors(List<String> errors) {
        this.errors = errors;
    }

    public List<String> getWarnings() {
        return warnings;
    }

    public void setWarnings(List<String> warnings) {
        this.warnings = warnings;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "ImportResultDto{" +
                "totalRecords=" + totalRecords +
                ", successfulInserts=" + successfulInserts +
                ", successfulUpdates=" + successfulUpdates +
                ", failedRecords=" + failedRecords +
                ", errors=" + errors +
                ", warnings=" + warnings +
                ", message='" + message + '\'' +
                '}';
    }
}
